package com.jcloud.admin.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jcloud.common.dto.CandlestickRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 财务统计控制器测试
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
public class FinancialStatsControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    public void testGetCandlestickData() throws Exception {
        // 构建测试请求
        CandlestickRequest request = new CandlestickRequest();
        request.setStartTime("2025-06-01 00:00:00");
        request.setEndTime("2025-08-31 23:59:59");
        request.setTimeframe("day");
        request.setDataType("revenue");
        request.setIncludeTechnicalIndicators(true);
        
        String requestJson = objectMapper.writeValueAsString(request);
        
        // 执行请求并验证响应
        mockMvc.perform(post("/financial/stats/candlestick-data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.candleData").isArray())
                .andExpect(jsonPath("$.data.statistics").exists())
                .andExpect(jsonPath("$.data.metadata").exists());
    }
    
    @Test
    public void testGetCandlestickDataWithInvalidRequest() throws Exception {
        // 构建无效请求（缺少必要参数）
        CandlestickRequest request = new CandlestickRequest();
        request.setStartTime(""); // 空的开始时间
        request.setEndTime("2025-08-31 23:59:59");
        request.setTimeframe("day");
        request.setDataType("revenue");
        
        String requestJson = objectMapper.writeValueAsString(request);
        
        // 执行请求并验证错误响应
        mockMvc.perform(post("/financial/stats/candlestick-data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    public void testGetCandlestickDataProfitType() throws Exception {
        // 测试利润类型K线数据
        CandlestickRequest request = new CandlestickRequest();
        request.setStartTime("2025-07-01 00:00:00");
        request.setEndTime("2025-07-31 23:59:59");
        request.setTimeframe("week");
        request.setDataType("profit");
        request.setIncludeTechnicalIndicators(false);
        
        String requestJson = objectMapper.writeValueAsString(request);
        
        mockMvc.perform(post("/financial/stats/candlestick-data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.metadata.dataType").value("profit"));
    }
    
    @Test
    public void testGetCandlestickDataCommissionType() throws Exception {
        // 测试佣金类型K线数据
        CandlestickRequest request = new CandlestickRequest();
        request.setStartTime("2025-08-01 00:00:00");
        request.setEndTime("2025-08-31 23:59:59");
        request.setTimeframe("month");
        request.setDataType("commission");
        request.setIncludeTechnicalIndicators(true);
        
        String requestJson = objectMapper.writeValueAsString(request);
        
        mockMvc.perform(post("/financial/stats/candlestick-data")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.metadata.dataType").value("commission"));
    }
}
