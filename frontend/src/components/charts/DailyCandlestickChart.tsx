/**
 * 日K线图组件
 * 基于真实历史财务数据显示每日的开盘、收盘、最高、最低值
 */

import React, { useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { RefreshCw, Download, Info } from 'lucide-react'
import { CandlestickChart } from './CandlestickChart'
import { 
  createRealFinancialCandlestickData,
  validateHistoricalData,
  type HistoricalFinancialDataPoint 
} from '@/utils/realFinancialChartUtils'

/**
 * 日K线图组件属性
 */
export interface DailyCandlestickChartProps {
  /** 历史财务数据 */
  historicalData: HistoricalFinancialDataPoint[]
  /** 启用的指标ID列表 */
  enabledIndicators: string[]
  /** 加载状态 */
  loading?: boolean
  /** 错误信息 */
  error?: string | null
  /** 自定义样式类名 */
  className?: string
  /** 主题 */
  theme?: 'light' | 'dark'
  /** 刷新回调 */
  onRefresh?: () => void
  /** 导出回调 */
  onExport?: () => void
}

/**
 * 日K线图组件
 */
export const DailyCandlestickChart: React.FC<DailyCandlestickChartProps> = ({
  historicalData,
  enabledIndicators,
  loading = false,
  error = null,
  className,
  theme = 'light',
  onRefresh,
  onExport
}) => {
  // 生成K线图数据
  const candlestickData = useMemo(() => {
    if (!historicalData.length || !enabledIndicators.length) {
      return []
    }

    return createRealFinancialCandlestickData(historicalData, enabledIndicators)
  }, [historicalData, enabledIndicators])

  // 验证历史数据
  const dataValidation = useMemo(() => {
    return validateHistoricalData(historicalData)
  }, [historicalData])

  // 渲染工具栏
  const renderToolbar = () => (
    <div className="flex items-center space-x-2">
      {onRefresh && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onRefresh}
          disabled={loading}
          className="h-8 w-8 p-0"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      )}
      {onExport && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onExport}
          disabled={loading || !!error}
          className="h-8 w-8 p-0"
        >
          <Download className="h-4 w-4" />
        </Button>
      )}
    </div>
  )

  // 渲染数据质量提示
  const renderDataQualityInfo = () => {
    if (dataValidation.isValid) {
      return (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-sm text-green-800">
              ✅ 基于 {historicalData.length} 天真实历史财务数据
            </span>
          </div>
        </div>
      )
    }

    return (
      <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Info className="w-4 h-4 text-yellow-600" />
            <span className="text-sm text-yellow-800 font-medium">数据质量提醒</span>
          </div>
          <ul className="text-xs text-yellow-700 space-y-1 ml-6">
            {dataValidation.issues.map((issue, index) => (
              <li key={index}>• {issue}</li>
            ))}
          </ul>
        </div>
      </div>
    )
  }

  // 渲染图表说明
  const renderChartExplanation = () => (
    <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-blue-900">日K线图说明：</h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• <span className="font-medium">红色蜡烛</span>：当日财务指标相比前日上升</li>
          <li>• <span className="font-medium">绿色蜡烛</span>：当日财务指标相比前日下降</li>
          <li>• <span className="font-medium">上影线</span>：当日最高值</li>
          <li>• <span className="font-medium">下影线</span>：当日最低值</li>
          <li>• <span className="font-medium">实体</span>：开盘值到收盘值的区间</li>
        </ul>
      </div>
    </div>
  )

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="flex items-center justify-center h-64 text-muted-foreground">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 opacity-50">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-4h2v18h-2V3zm4 9h2v9h-2v-9zm4-3h2v12h-2V9z"/>
          </svg>
        </div>
        <p className="text-lg font-medium mb-2">暂无K线图数据</p>
        <p className="text-sm">请选择财务指标并确保有历史数据</p>
      </div>
    </div>
  )

  // 渲染加载状态
  const renderLoadingState = () => (
    <div className="flex items-center justify-center h-64 text-muted-foreground">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
        <p>正在生成K线图数据...</p>
      </div>
    </div>
  )

  // 渲染错误状态
  const renderErrorState = () => (
    <div className="flex items-center justify-center h-64 text-red-500">
      <div className="text-center">
        <div className="w-16 h-16 mx-auto mb-4 opacity-50">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
        </div>
        <p className="text-lg font-medium mb-2">数据加载失败</p>
        <p className="text-sm">{error}</p>
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            className="mt-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            重试
          </Button>
        )}
      </div>
    </div>
  )

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <div className="w-5 h-5 mr-2">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-4h2v18h-2V3zm4 9h2v9h-2v-9zm4-3h2v12h-2V9z"/>
              </svg>
            </div>
            日K线图
            <span className="ml-2 text-sm font-normal text-muted-foreground">
              ({enabledIndicators.length} 项指标)
            </span>
          </CardTitle>
          {renderToolbar()}
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          renderLoadingState()
        ) : error ? (
          renderErrorState()
        ) : !candlestickData.length ? (
          renderEmptyState()
        ) : (
          <>
            {renderDataQualityInfo()}
            {renderChartExplanation()}
            <CandlestickChart
              data={candlestickData}
              title=""
              loading={false}
              error={null}
              theme={theme}
              showMA={false} // 日K线图不显示移动平均线
              showVolume={true}
              height={500}
            />
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default DailyCandlestickChart
