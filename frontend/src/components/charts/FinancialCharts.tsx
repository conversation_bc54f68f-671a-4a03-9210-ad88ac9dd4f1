/**
 * 财务图表组件集合
 * 包含折线图、柱状图、饼图、面积图等常用图表类型
 */

import React, { useMemo } from 'react'
import { ChartContainer } from './ChartContainer'
import { convertFinancialDataToChartData } from '@/utils/chartUtils'
import type { FinancialChartDataItem, ChartConfig } from '@/types/charts'

/**
 * 基础财务图表属性
 */
interface BaseFinancialChartProps {
  /** 财务数据 */
  data: FinancialChartDataItem[]
  /** 图表标题 */
  title?: string
  /** 宽度 */
  width?: number | string
  /** 高度 */
  height?: number | string
  /** 加载状态 */
  loading?: boolean
  /** 错误信息 */
  error?: string | null
  /** 自定义样式类名 */
  className?: string
  /** 主题 */
  theme?: 'light' | 'dark'
  /** 图表点击事件 */
  onChartClick?: (params: any) => void
  /** 图表悬停事件 */
  onChartHover?: (params: any) => void
  /** 刷新回调 */
  onRefresh?: () => void
}

/**
 * 折线图组件
 */
const LineChart: React.FC<BaseFinancialChartProps> = ({
  data,
  title = '趋势分析',
  width = '100%',
  height = 400,
  loading = false,
  error = null,
  className,
  theme = 'light',
  onChartClick,
  onChartHover,
  onRefresh
}) => {
  const { chartConfig, chartData } = useMemo(() => {
    const config: ChartConfig = {
      type: 'line',
      width,
      height,
      responsive: true,
      theme,
      showLegend: false,
      showToolbox: true,
      zoomable: true,
      title
    }

    const chartData = convertFinancialDataToChartData(data, 'line')

    return { chartConfig: config, chartData }
  }, [data, width, height, theme, title])

  return (
    <ChartContainer
      data={chartData}
      config={chartConfig}
      loading={loading}
      error={error}
      className={className}
      title={title}
      theme={theme}
      onChartClick={onChartClick}
      onChartHover={onChartHover}
      onRefresh={onRefresh}
    />
  )
}

/**
 * 柱状图组件
 */
const BarChart: React.FC<BaseFinancialChartProps> = ({
  data,
  title = '数据对比',
  width = '100%',
  height = 400,
  loading = false,
  error = null,
  className,
  theme = 'light',
  onChartClick,
  onChartHover,
  onRefresh
}) => {
  const { chartConfig, chartData } = useMemo(() => {
    const config: ChartConfig = {
      type: 'bar',
      width,
      height,
      responsive: true,
      theme,
      showLegend: false,
      showToolbox: true,
      zoomable: false,
      title
    }

    const chartData = convertFinancialDataToChartData(data, 'bar')

    return { chartConfig: config, chartData }
  }, [data, width, height, theme, title])

  return (
    <ChartContainer
      data={chartData}
      config={chartConfig}
      loading={loading}
      error={error}
      className={className}
      title={title}
      theme={theme}
      onChartClick={onChartClick}
      onChartHover={onChartHover}
      onRefresh={onRefresh}
    />
  )
}

/**
 * 饼图组件
 */
const PieChart: React.FC<BaseFinancialChartProps> = ({
  data,
  title = '占比分析',
  width = '100%',
  height = 400,
  loading = false,
  error = null,
  className,
  theme = 'light',
  onChartClick,
  onChartHover,
  onRefresh
}) => {
  const { chartConfig, chartData } = useMemo(() => {
    const config: ChartConfig = {
      type: 'pie',
      width,
      height,
      responsive: true,
      theme,
      showLegend: true,
      showToolbox: true,
      zoomable: false,
      title
    }

    const chartData = convertFinancialDataToChartData(data, 'pie')

    return { chartConfig: config, chartData }
  }, [data, width, height, theme, title])

  return (
    <ChartContainer
      data={chartData}
      config={chartConfig}
      loading={loading}
      error={error}
      className={className}
      title={title}
      theme={theme}
      onChartClick={onChartClick}
      onChartHover={onChartHover}
      onRefresh={onRefresh}
    />
  )
}

/**
 * 面积图组件
 */
const AreaChart: React.FC<BaseFinancialChartProps> = ({
  data,
  title = '累积数据展示',
  width = '100%',
  height = 400,
  loading = false,
  error = null,
  className,
  theme = 'light',
  onChartClick,
  onChartHover,
  onRefresh
}) => {
  const { chartConfig, chartData } = useMemo(() => {
    const config: ChartConfig = {
      type: 'area',
      width,
      height,
      responsive: true,
      theme,
      showLegend: false,
      showToolbox: true,
      zoomable: true,
      title
    }

    const chartData = convertFinancialDataToChartData(data, 'area')

    return { chartConfig: config, chartData }
  }, [data, width, height, theme, title])

  return (
    <ChartContainer
      data={chartData}
      config={chartConfig}
      loading={loading}
      error={error}
      className={className}
      title={title}
      theme={theme}
      onChartClick={onChartClick}
      onChartHover={onChartHover}
      onRefresh={onRefresh}
    />
  )
}

/**
 * 多图表组合组件
 */
export interface MultiChartProps extends BaseFinancialChartProps {
  /** 图表类型列表 */
  chartTypes: ('line' | 'bar' | 'pie' | 'area')[]
  /** 每行显示的图表数量 */
  chartsPerRow?: number
}

const MultiChart: React.FC<MultiChartProps> = ({
  data,
  chartTypes,
  chartsPerRow = 2,
  title = '综合分析',
  height = 300,
  loading = false,
  error = null,
  className,
  theme = 'light',
  onChartClick,
  onChartHover,
  onRefresh
}) => {
  const chartComponents = useMemo(() => {
    const components = []
    
    for (const chartType of chartTypes) {
      let ChartComponent
      let chartTitle
      
      switch (chartType) {
        case 'line':
          ChartComponent = LineChart
          chartTitle = '趋势分析'
          break
        case 'bar':
          ChartComponent = BarChart
          chartTitle = '数据对比'
          break
        case 'pie':
          ChartComponent = PieChart
          chartTitle = '占比分析'
          break
        case 'area':
          ChartComponent = AreaChart
          chartTitle = '累积展示'
          break
        default:
          continue
      }
      
      components.push(
        <div key={chartType} className="w-full">
          <ChartComponent
            data={data}
            title={chartTitle}
            height={height}
            loading={loading}
            error={error}
            theme={theme}
            onChartClick={onChartClick}
            onChartHover={onChartHover}
            onRefresh={onRefresh}
          />
        </div>
      )
    }
    
    return components
  }, [data, chartTypes, height, loading, error, theme, onChartClick, onChartHover, onRefresh])

  const gridCols = chartsPerRow === 1 ? 'grid-cols-1' : 
                   chartsPerRow === 2 ? 'grid-cols-1 lg:grid-cols-2' :
                   chartsPerRow === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                   'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'

  return (
    <div className={className}>
      {title && (
        <h2 className="text-xl font-semibold mb-4">{title}</h2>
      )}
      <div className={`grid gap-6 ${gridCols}`}>
        {chartComponents}
      </div>
    </div>
  )
}
// 命名导出
export {
  LineChart,
  BarChart,
  PieChart,
  AreaChart,
  MultiChart
}

export default {
  LineChart,
  BarChart,
  PieChart,
  AreaChart,
  MultiChart
}
