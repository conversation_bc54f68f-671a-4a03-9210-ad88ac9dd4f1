/**
 * 图表容器组件
 * 提供统一的图表包装器，包含加载状态、错误处理、响应式布局等功能
 */

import React, { useRef, useEffect, useState } from 'react'
import ReactECharts from 'echarts-for-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Loader2, Download, Maximize2, RefreshCw } from 'lucide-react'
import { cn } from '@/utils'
import { generateEChartsOption, exportChartAsImage, DEFAULT_CHART_THEME, DARK_CHART_THEME } from '@/utils/chartUtils'
import type { ChartProps } from '@/types/charts'

/**
 * 图表容器组件属性
 */
export interface ChartContainerProps extends ChartProps {
  /** 图表标题 */
  title?: string
  /** 是否显示工具栏 */
  showToolbar?: boolean
  /** 是否显示刷新按钮 */
  showRefresh?: boolean
  /** 是否显示导出按钮 */
  showExport?: boolean
  /** 是否显示全屏按钮 */
  showFullscreen?: boolean
  /** 刷新回调 */
  onRefresh?: () => void
  /** 全屏回调 */
  onFullscreen?: () => void
  /** 主题模式 */
  theme?: 'light' | 'dark'
}

/**
 * 图表容器组件
 */
export const ChartContainer: React.FC<ChartContainerProps> = ({
  data,
  config,
  loading = false,
  error = null,
  className,
  title,
  showToolbar = true,
  showRefresh = true,
  showExport = true,
  showFullscreen = false,
  onChartClick,
  onChartHover,
  onChartZoom,
  onRefresh,
  onFullscreen,
  theme = 'light'
}) => {
  const chartRef = useRef<ReactECharts>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [chartSize, setChartSize] = useState({ width: '100%', height: 400 })
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 })

  // 生成ECharts配置
  const chartOption = generateEChartsOption(
    data,
    config,
    theme === 'dark' ? DARK_CHART_THEME : DEFAULT_CHART_THEME
  )

  // 处理容器大小变化和响应式
  useEffect(() => {
    const updateContainerSize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect()
        setContainerSize({ width, height })
      }
    }

    const handleResize = () => {
      updateContainerSize()
      if (chartRef.current) {
        // 延迟调用resize以确保容器尺寸已更新
        setTimeout(() => {
          chartRef.current?.getEchartsInstance().resize()
        }, 100)
      }
    }

    // 初始化容器尺寸
    updateContainerSize()

    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)

    // 使用ResizeObserver监听容器大小变化（如果支持）
    let resizeObserver: ResizeObserver | null = null
    if (window.ResizeObserver && containerRef.current) {
      resizeObserver = new ResizeObserver(handleResize)
      resizeObserver.observe(containerRef.current)
    }

    return () => {
      window.removeEventListener('resize', handleResize)
      if (resizeObserver) {
        resizeObserver.disconnect()
      }
    }
  }, [])

  // 处理全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange)
  }, [])

  // 导出图表
  const handleExport = () => {
    if (chartRef.current) {
      const chartInstance = chartRef.current.getEchartsInstance()
      exportChartAsImage(chartInstance, title || 'chart', 'png')
    }
  }

  // 切换全屏
  const handleFullscreen = () => {
    if (!isFullscreen) {
      const element = chartRef.current?.ele
      if (element?.requestFullscreen) {
        element.requestFullscreen()
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      }
    }
    onFullscreen?.()
  }

  // 图表事件处理
  const chartEvents = {
    click: onChartClick,
    mouseover: onChartHover,
    dataZoom: onChartZoom
  }

  // 渲染工具栏
  const renderToolbar = () => {
    if (!showToolbar) return null

    return (
      <div className="flex items-center space-x-2">
        {showRefresh && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onRefresh}
            disabled={loading}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
          </Button>
        )}
        {showExport && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleExport}
            disabled={loading || !!error}
            className="h-8 w-8 p-0"
          >
            <Download className="h-4 w-4" />
          </Button>
        )}
        {showFullscreen && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleFullscreen}
            disabled={loading || !!error}
            className="h-8 w-8 p-0"
          >
            <Maximize2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    )
  }

  // 渲染加载状态
  const renderLoading = () => (
    <div className="flex items-center justify-center h-64">
      <div className="flex items-center space-x-2 text-muted-foreground">
        <Loader2 className="h-5 w-5 animate-spin" />
        <span>加载图表数据中...</span>
      </div>
    </div>
  )

  // 渲染错误状态
  const renderError = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="text-red-500 mb-2">图表加载失败</div>
        <div className="text-sm text-muted-foreground">{error}</div>
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            className="mt-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            重试
          </Button>
        )}
      </div>
    </div>
  )

  // 渲染空数据状态
  const renderEmpty = () => (
    <div className="flex items-center justify-center h-64">
      <div className="text-center text-muted-foreground">
        <div className="mb-2">暂无图表数据</div>
        <div className="text-sm">请选择要显示的统计项</div>
      </div>
    </div>
  )

  return (
    <Card className={cn('w-full', className)}>
      {(title || showToolbar) && (
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          {title && (
            <CardTitle className="text-lg font-medium">
              {title}
            </CardTitle>
          )}
          {renderToolbar()}
        </CardHeader>
      )}
      <CardContent className="p-0">
        {loading ? (
          renderLoading()
        ) : error ? (
          renderError()
        ) : !data.series.length || data.series.every(s => !s.data.length) ? (
          renderEmpty()
        ) : (
          <div ref={containerRef} className="w-full">
            <ReactECharts
              ref={chartRef}
              option={chartOption}
              style={{
                width: '100%',
                height: typeof config.height === 'number' ? `${config.height}px` : config.height || '400px',
                minHeight: '300px'
              }}
              onEvents={chartEvents}
              opts={{
                renderer: 'canvas',
                useDirtyRect: false
              }}
            />
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 简化版图表容器
 * 不包含卡片包装器，适用于嵌入其他组件中
 */
export const SimpleChartContainer: React.FC<ChartProps> = ({
  data,
  config,
  loading = false,
  error = null,
  className,
  onChartClick,
  onChartHover,
  onChartZoom
}) => {
  const chartRef = useRef<ReactECharts>(null)

  const chartOption = generateEChartsOption(data, config)

  const chartEvents = {
    click: onChartClick,
    mouseover: onChartHover,
    dataZoom: onChartZoom
  }

  useEffect(() => {
    const handleResize = () => {
      if (chartRef.current) {
        chartRef.current.getEchartsInstance().resize()
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  if (loading) {
    return (
      <div className={cn("flex items-center justify-center h-64", className)}>
        <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn("flex items-center justify-center h-64 text-red-500", className)}>
        {error}
      </div>
    )
  }

  return (
    <div className={cn("w-full", className)}>
      <ReactECharts
        ref={chartRef}
        option={chartOption}
        style={{ width: '100%', height: '400px' }}
        onEvents={chartEvents}
        opts={{
          renderer: 'canvas',
          useDirtyRect: false
        }}
      />
    </div>
  )
}

export default ChartContainer
