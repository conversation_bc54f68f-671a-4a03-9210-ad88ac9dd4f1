/**
 * 日均线图组件
 * 显示财务指标的移动平均趋势，基于真实历史数据
 */

import React, { useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { RefreshCw, Download, TrendingUp, Info } from 'lucide-react'
import { LineChart } from './FinancialCharts'
import { 
  convertHistoricalDataToTimeSeries,
  validateHistoricalData,
  type HistoricalFinancialDataPoint,
  type FinancialTimeSeries 
} from '@/utils/realFinancialChartUtils'
import type { FinancialChartDataItem } from '@/types/charts'

/**
 * 移动平均线配置
 */
interface MovingAverageConfig {
  period: number
  label: string
  color: string
  enabled: boolean
}

/**
 * 日均线图组件属性
 */
export interface DailyMovingAverageChartProps {
  /** 历史财务数据 */
  historicalData: HistoricalFinancialDataPoint[]
  /** 启用的指标ID列表 */
  enabledIndicators: string[]
  /** 加载状态 */
  loading?: boolean
  /** 错误信息 */
  error?: string | null
  /** 自定义样式类名 */
  className?: string
  /** 主题 */
  theme?: 'light' | 'dark'
  /** 刷新回调 */
  onRefresh?: () => void
  /** 导出回调 */
  onExport?: () => void
}

/**
 * 默认移动平均线配置
 */
const DEFAULT_MA_CONFIG: MovingAverageConfig[] = [
  { period: 5, label: '5日均线', color: '#ef4444', enabled: true },
  { period: 10, label: '10日均线', color: '#3b82f6', enabled: true },
  { period: 20, label: '20日均线', color: '#10b981', enabled: false },
  { period: 30, label: '30日均线', color: '#f59e0b', enabled: false }
]

/**
 * 计算移动平均线
 */
const calculateMovingAverage = (
  timeSeries: FinancialTimeSeries,
  period: number
): FinancialChartDataItem[] => {
  const result: FinancialChartDataItem[] = []
  const dataPoints = timeSeries.dataPoints

  for (let i = 0; i < dataPoints.length; i++) {
    if (i < period - 1) {
      // 数据点不足，跳过
      continue
    }

    // 计算移动平均值
    let sum = 0
    for (let j = i - period + 1; j <= i; j++) {
      sum += dataPoints[j].value
    }
    const avgValue = sum / period

    result.push({
      id: `${timeSeries.category}_${timeSeries.name}_ma${period}`,
      name: `${timeSeries.name} MA${period}`,
      value: Math.round(avgValue * 100) / 100,
      unit: timeSeries.unit,
      timestamp: dataPoints[i].timestamp,
      category: timeSeries.category,
      showInChart: true
    })
  }

  return result
}

/**
 * 日均线图组件
 */
export const DailyMovingAverageChart: React.FC<DailyMovingAverageChartProps> = ({
  historicalData,
  enabledIndicators,
  loading = false,
  error = null,
  className,
  theme = 'light',
  onRefresh,
  onExport
}) => {
  const [maConfig, setMaConfig] = React.useState<MovingAverageConfig[]>(DEFAULT_MA_CONFIG)
  const [selectedIndicator, setSelectedIndicator] = React.useState<string>('')

  // 转换历史数据为时间序列
  const timeSeries = useMemo(() => {
    if (!historicalData.length) return []
    return convertHistoricalDataToTimeSeries(historicalData)
  }, [historicalData])

  // 过滤启用的指标
  const enabledTimeSeries = useMemo(() => {
    return timeSeries.filter(series => {
      const key = `${series.category}_${series.name}`
      return enabledIndicators.includes(key)
    })
  }, [timeSeries, enabledIndicators])

  // 生成移动平均线数据
  const movingAverageData = useMemo(() => {
    if (!enabledTimeSeries.length) return []

    const targetSeries = selectedIndicator 
      ? enabledTimeSeries.find(s => `${s.category}_${s.name}` === selectedIndicator)
      : enabledTimeSeries[0]

    if (!targetSeries) return []

    const allMaData: FinancialChartDataItem[] = []

    // 添加原始数据线
    targetSeries.dataPoints.forEach(point => {
      allMaData.push({
        id: `${targetSeries.category}_${targetSeries.name}_original`,
        name: targetSeries.name,
        value: point.value,
        unit: targetSeries.unit,
        timestamp: point.timestamp,
        category: targetSeries.category,
        showInChart: true
      })
    })

    // 添加移动平均线
    maConfig.forEach(config => {
      if (config.enabled) {
        const maData = calculateMovingAverage(targetSeries, config.period)
        allMaData.push(...maData)
      }
    })

    return allMaData
  }, [enabledTimeSeries, selectedIndicator, maConfig])

  // 验证历史数据
  const dataValidation = useMemo(() => {
    return validateHistoricalData(historicalData)
  }, [historicalData])

  // 渲染工具栏
  const renderToolbar = () => (
    <div className="flex items-center space-x-2">
      {onRefresh && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onRefresh}
          disabled={loading}
          className="h-8 w-8 p-0"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
        </Button>
      )}
      {onExport && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onExport}
          disabled={loading || !!error}
          className="h-8 w-8 p-0"
        >
          <Download className="h-4 w-4" />
        </Button>
      )}
    </div>
  )

  // 渲染指标选择器
  const renderIndicatorSelector = () => {
    if (!enabledTimeSeries.length) return null

    return (
      <div className="flex items-center space-x-2">
        <Label className="text-sm font-medium">选择指标:</Label>
        <Select 
          value={selectedIndicator || `${enabledTimeSeries[0].category}_${enabledTimeSeries[0].name}`} 
          onValueChange={setSelectedIndicator}
        >
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {enabledTimeSeries.map(series => {
              const key = `${series.category}_${series.name}`
              return (
                <SelectItem key={key} value={key}>
                  {series.name}
                </SelectItem>
              )
            })}
          </SelectContent>
        </Select>
      </div>
    )
  }

  // 渲染移动平均线配置
  const renderMaConfig = () => (
    <div className="space-y-2">
      <Label className="text-sm font-medium">移动平均线:</Label>
      <div className="grid grid-cols-2 gap-2">
        {maConfig.map((config, index) => (
          <div key={config.period} className="flex items-center space-x-2">
            <Switch
              checked={config.enabled}
              onCheckedChange={(enabled) => {
                const newConfig = [...maConfig]
                newConfig[index] = { ...config, enabled }
                setMaConfig(newConfig)
              }}
            />
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: config.color }}
            />
            <Label className="text-xs">{config.label}</Label>
          </div>
        ))}
      </div>
    </div>
  )

  // 渲染图表说明
  const renderChartExplanation = () => (
    <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-blue-900">移动平均线说明：</h4>
        <ul className="text-xs text-blue-700 space-y-1">
          <li>• <span className="font-medium">原始数据线</span>：显示财务指标的实际每日数值</li>
          <li>• <span className="font-medium">5日均线</span>：短期趋势，反应快速变化</li>
          <li>• <span className="font-medium">10日均线</span>：中短期趋势，平滑日常波动</li>
          <li>• <span className="font-medium">20日均线</span>：中期趋势，识别主要方向</li>
          <li>• <span className="font-medium">30日均线</span>：长期趋势，过滤短期噪音</li>
        </ul>
      </div>
    </div>
  )

  // 渲染空状态
  const renderEmptyState = () => (
    <div className="flex items-center justify-center h-64 text-muted-foreground">
      <div className="text-center">
        <TrendingUp className="w-16 h-16 mx-auto mb-4 opacity-50" />
        <p className="text-lg font-medium mb-2">暂无均线图数据</p>
        <p className="text-sm">请选择财务指标并确保有历史数据</p>
      </div>
    </div>
  )

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            日均线图
            <span className="ml-2 text-sm font-normal text-muted-foreground">
              ({enabledIndicators.length} 项指标)
            </span>
          </CardTitle>
          {renderToolbar()}
        </div>
        
        {/* 控制面板 */}
        <div className="space-y-4 pt-4 border-t">
          <div className="flex flex-col lg:flex-row lg:items-start gap-4">
            {renderIndicatorSelector()}
            {renderMaConfig()}
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center h-64 text-muted-foreground">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p>正在生成均线图数据...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-64 text-red-500">
            <div className="text-center">
              <p className="text-lg font-medium mb-2">数据加载失败</p>
              <p className="text-sm">{error}</p>
            </div>
          </div>
        ) : !movingAverageData.length ? (
          renderEmptyState()
        ) : (
          <>
            {!dataValidation.isValid && (
              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Info className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">
                    数据质量提醒: {dataValidation.issues.join(', ')}
                  </span>
                </div>
              </div>
            )}
            {renderChartExplanation()}
            <LineChart
              data={movingAverageData}
              title=""
              loading={false}
              error={null}
              theme={theme}
              height={500}
            />
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default DailyMovingAverageChart
