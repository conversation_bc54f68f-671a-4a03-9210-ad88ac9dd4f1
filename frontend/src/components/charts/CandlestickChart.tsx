import React, { useEffect, useRef, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  Download,
  Settings,
  ZoomIn,
  ZoomOut,
  RotateCcw
} from 'lucide-react'
import type { CandlestickChartProps } from '@/types/candlestick'
import { formatPrice, formatVolume, formatPercent } from '@/utils/candlestickUtils'

/**
 * K线图主组件
 * 提供完整的K线图展示功能，包括OHLC数据、技术指标、交互控制等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
export const CandlestickChart: React.FC<CandlestickChartProps> = ({
  data,
  loading = false,
  className = '',
  title = 'K线图',
  height = 500,
  showVolume = true,
  showTechnicalIndicators = false,
  technicalIndicators = [],
  timeframe = 'day',
  onTimeframeChange,
  dataType = 'revenue',
  onDataTypeChange
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstanceRef = useRef<any>(null)

  // 数据转换和处理
  const chartData = useMemo(() => {
    if (!data || data.length === 0) return null

    // 转换为ECharts需要的格式
    const candleData = data.map(item => [
      item.open,
      item.close,
      item.low,
      item.high
    ])

    const volumeData = data.map(item => item.volume)
    const categories = data.map(item => item.date)

    return {
      candleData,
      volumeData,
      categories,
      rawData: data
    }
  }, [data])

  // 统计信息计算
  const statistics = useMemo(() => {
    if (!data || data.length === 0) return null

    const latest = data[data.length - 1]
    const previous = data.length > 1 ? data[data.length - 2] : latest
    
    const change = latest.close - previous.close
    const changePercent = previous.close !== 0 ? (change / previous.close) * 100 : 0
    
    const maxHigh = Math.max(...data.map(item => item.high))
    const minLow = Math.min(...data.map(item => item.low))
    const totalVolume = data.reduce((sum, item) => sum + item.volume, 0)

    return {
      latest: latest.close,
      change,
      changePercent,
      maxHigh,
      minLow,
      totalVolume,
      avgVolume: totalVolume / data.length,
      isRising: change >= 0
    }
  }, [data])

  // ECharts配置
  const chartOption = useMemo(() => {
    if (!chartData) return null

    const option = {
      animation: true,
      backgroundColor: 'transparent',
      grid: [
        {
          left: '10%',
          right: '8%',
          top: '15%',
          height: showVolume ? '50%' : '70%'
        },
        ...(showVolume ? [{
          left: '10%',
          right: '8%',
          top: '70%',
          height: '15%'
        }] : [])
      ],
      xAxis: [
        {
          type: 'category',
          data: chartData.categories,
          boundaryGap: false,
          axisLine: { onZero: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax',
          axisPointer: {
            z: 100
          }
        },
        ...(showVolume ? [{
          type: 'category',
          gridIndex: 1,
          data: chartData.categories,
          boundaryGap: false,
          axisLine: { onZero: false },
          axisTick: { show: false },
          splitLine: { show: false },
          axisLabel: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        }] : [])
      ],
      yAxis: [
        {
          scale: true,
          splitArea: {
            show: true
          }
        },
        ...(showVolume ? [{
          scale: true,
          gridIndex: 1,
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }] : [])
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: showVolume ? [0, 1] : [0],
          start: 50,
          end: 100
        },
        {
          show: true,
          xAxisIndex: showVolume ? [0, 1] : [0],
          type: 'slider',
          top: '85%',
          start: 50,
          end: 100
        }
      ],
      series: [
        {
          name: 'K线',
          type: 'candlestick',
          data: chartData.candleData,
          itemStyle: {
            color: '#10b981', // 上涨颜色
            color0: '#ef4444', // 下跌颜色
            borderColor: '#10b981',
            borderColor0: '#ef4444'
          },
          emphasis: {
            itemStyle: {
              color: '#059669',
              color0: '#dc2626',
              borderColor: '#059669',
              borderColor0: '#dc2626'
            }
          }
        },
        ...(showVolume ? [{
          name: '成交量',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: chartData.volumeData,
          itemStyle: {
            color: function(params: any) {
              const dataIndex = params.dataIndex
              if (dataIndex === 0) return '#6b7280'
              
              const current = chartData.rawData[dataIndex]
              const previous = chartData.rawData[dataIndex - 1]
              
              return current.close >= previous.close ? '#10b981' : '#ef4444'
            }
          }
        }] : []),
        // 技术指标线
        ...technicalIndicators.map((indicator, index) => ({
          name: indicator.name,
          type: 'line',
          data: indicator.data,
          smooth: true,
          lineStyle: {
            color: indicator.color,
            width: 2
          },
          showSymbol: false,
          emphasis: {
            focus: 'series'
          }
        }))
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        textStyle: {
          color: '#374151'
        },
        formatter: function(params: any) {
          const dataIndex = params[0].dataIndex
          const item = chartData.rawData[dataIndex]
          
          let html = `<div class="p-2">
            <div class="font-semibold mb-2">${item.date}</div>
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div>开盘: ${formatPrice(item.open)}</div>
              <div>收盘: ${formatPrice(item.close)}</div>
              <div>最高: ${formatPrice(item.high)}</div>
              <div>最低: ${formatPrice(item.low)}</div>
              <div>成交量: ${formatVolume(item.volume)}</div>
              <div>涨跌幅: ${formatPercent(((item.close - item.open) / item.open) * 100)}</div>
            </div>
          </div>`
          
          return html
        }
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: false
          },
          brush: {
            type: ['lineX', 'clear']
          }
        }
      }
    }

    return option
  }, [chartData, showVolume, technicalIndicators])

  // 初始化和更新图表
  useEffect(() => {
    if (!chartRef.current || !chartOption) return

    // 动态导入ECharts（避免SSR问题）
    const initChart = async () => {
      try {
        const echarts = await import('echarts')

        if (chartInstanceRef.current) {
          chartInstanceRef.current.dispose()
        }

        chartInstanceRef.current = echarts.init(chartRef.current)
        chartInstanceRef.current.setOption(chartOption, true)

        // 监听窗口大小变化
        const handleResize = () => {
          chartInstanceRef.current?.resize()
        }
        window.addEventListener('resize', handleResize)

        return () => {
          window.removeEventListener('resize', handleResize)
        }
      } catch (error) {
        console.error('ECharts加载失败:', error)
      }
    }

    initChart()
  }, [chartOption])

  // 清理函数
  useEffect(() => {
    return () => {
      chartInstanceRef.current?.dispose()
    }
  }, [])

  // 时间粒度选项
  const timeframeOptions = [
    { value: 'day', label: '日K' },
    { value: 'week', label: '周K' },
    { value: 'month', label: '月K' }
  ]

  // 数据类型选项
  const dataTypeOptions = [
    { value: 'revenue', label: '收入' },
    { value: 'profit', label: '利润' },
    { value: 'commission', label: '佣金' }
  ]

  // 导出数据
  const handleExport = () => {
    if (!chartInstanceRef.current) return
    
    const url = chartInstanceRef.current.getDataURL({
      pixelRatio: 2,
      backgroundColor: '#fff'
    })
    
    const link = document.createElement('a')
    link.download = `${title}_${timeframe}_${dataType}_${new Date().toISOString().slice(0, 10)}.png`
    link.href = url
    link.click()
  }

  // 重置缩放
  const handleResetZoom = () => {
    chartInstanceRef.current?.dispatchAction({
      type: 'dataZoom',
      start: 0,
      end: 100
    })
  }

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
        </CardHeader>
        <CardContent>
          <Skeleton className="w-full" style={{ height: `${height}px` }} />
        </CardContent>
      </Card>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <BarChart3 className="h-4 w-4" />
            <AlertDescription>
              暂无K线数据，请选择其他时间范围或数据类型
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {title}
            </CardTitle>
            
            {statistics && (
              <div className="flex items-center gap-2">
                <Badge variant={statistics.isRising ? "default" : "destructive"}>
                  {statistics.isRising ? (
                    <TrendingUp className="h-3 w-3 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 mr-1" />
                  )}
                  {formatPrice(statistics.latest)}
                </Badge>
                <span className={`text-sm font-medium ${
                  statistics.isRising ? 'text-green-600' : 'text-red-600'
                }`}>
                  {formatPercent(statistics.changePercent)}
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Select value={timeframe} onValueChange={onTimeframeChange}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {timeframeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={dataType} onValueChange={onDataTypeChange}>
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {dataTypeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button variant="outline" size="sm" onClick={handleResetZoom}>
              <RotateCcw className="h-4 w-4" />
            </Button>

            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div 
          ref={chartRef} 
          className="w-full"
          style={{ height: `${height}px` }}
        />
        
        {statistics && (
          <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">最高价:</span>
              <span className="ml-2 font-medium">{formatPrice(statistics.maxHigh)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">最低价:</span>
              <span className="ml-2 font-medium">{formatPrice(statistics.minLow)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">总成交量:</span>
              <span className="ml-2 font-medium">{formatVolume(statistics.totalVolume)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">平均成交量:</span>
              <span className="ml-2 font-medium">{formatVolume(statistics.avgVolume)}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
